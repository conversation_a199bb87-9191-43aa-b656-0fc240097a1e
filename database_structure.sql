-- 充值和余额支付相关数据库结构

-- 1. 为用户表添加余额字段
ALTER TABLE `fzh_user` ADD COLUMN `balance` DECIMAL(10,2) DEFAULT 0.00 COMMENT '用户余额';

-- 2. 创建充值记录表
CREATE TABLE `fzh_recharge_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recharge_id` varchar(32) NOT NULL COMMENT '充值订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `recharge_amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '实际支付金额',
  `pay_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付方式：1-微信支付',
  `pay_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付状态：1-待支付，2-支付成功，3-支付失败',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `recharge_id` (`recharge_id`),
  KEY `user_id` (`user_id`),
  KEY `pay_status` (`pay_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 3. 创建余额支付记录表
CREATE TABLE `fzh_balance_pay_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pay_id` varchar(32) NOT NULL COMMENT '支付记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '支付前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '支付后余额',
  `pay_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付状态：1-支付成功，2-支付失败，3-已退款',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `pay_id` (`pay_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `pay_status` (`pay_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额支付记录表';

-- 4. 创建用户余额变动记录表
CREATE TABLE `fzh_balance_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `change_type` tinyint(1) NOT NULL COMMENT '变动类型：1-充值，2-支付，3-退款',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `related_id` varchar(32) DEFAULT NULL COMMENT '关联ID（充值订单号或支付订单号）',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `change_type` (`change_type`),
  KEY `related_id` (`related_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额变动记录表';

-- 5. 订单表
CREATE TABLE `fzh_order` (
  `order_id` char(20) NOT NULL DEFAULT '' COMMENT '订单ID，DD前缀',
  `order_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单类型：1消费订单，3充值订单',
  `order_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态',
  `org_id` int(11) NOT NULL DEFAULT '0' COMMENT '商户ID',
  `machine_id` int(11) DEFAULT '0' COMMENT '设备ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '微信userid',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `service_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `order_score` int(11) NOT NULL DEFAULT '0' COMMENT '订单积分',
  `pay_id` smallint(6) NOT NULL DEFAULT '0' COMMENT '支付方式：1、微信',
  `pay_status` tinyint(4) DEFAULT '1' COMMENT '支付状态：1、未支付，2、已支付',
  `is_invoice` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否需要发票 1:需要,0:不需要',
  `order_remark` varchar(255) DEFAULT NULL COMMENT '客户备注',
  `close_reason` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单关闭原因',
  `del` tinyint(2) DEFAULT '0' COMMENT '删除状态',
  `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_o` (`org_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单表';
