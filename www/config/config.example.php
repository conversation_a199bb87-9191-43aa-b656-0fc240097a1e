<?php
/* 
 * 常用配置信息
 */

return array(

    //Redis
    'redis' => array (
        'enable' => true,
        'host' => '127.0.0.1',
        'port' => '6379',
        'namespace' => '',
    ),

    //DB
    'fzhl_db' => array(
        'dbhost' => '127.0.0.1',
        'dbname' => 'fzhl_shop_db',
        'dbuser' => 'root',
        'dbpass' => '',
        'dbport' => '3306',
        'charset' => 'utf8',
    ),

    //ERP图片资源域名路径
    'img_host' => '',
    
    //微信配置
    'wx_small_config' => array(
        'wx_app_id' => 'wx9dbf41e2035c1de5', //小程序appid
        'wx_app_secret' => '66cdb90f321b00d073ee2aff5e9f606b', //小程序secret
        'wx_mch_id' => '1722665336', //ma授权商户号
        'wx_app_key' => 'bzSg9sFsLWy2tVnfP8rR9Zs6bZfgZfQW', //ma授权商户号秘钥key
        'notify' => 'https://fzhl-api.bjxsw.cn/mini/v1/WXNotify', //回调链接
        'env_version' => 'trial', //小程序环境版本
    ),

    //审核状态
    'audit_status' => 1,
);



?>
