<?php

/**
 * RechargeNotify 充值微信支付回调
 * <AUTHOR>
 */
class RechargeNotify extends BaseController {

    public function run() {
        $time = time();
        
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        
        //获取微信回调数据
        $xml = file_get_contents('php://input');
        $xmlArr = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        
        //验证回调数据
        if(empty($xmlArr) || !isset($xmlArr['return_code'])){
            echo 'FAIL';
            exit;
        }
        
        //提取关键信息
        $return_code = $xmlArr['return_code'];
        $result_code = isset($xmlArr['result_code']) ? $xmlArr['result_code'] : '';
        $out_trade_no = isset($xmlArr['out_trade_no']) ? $xmlArr['out_trade_no'] : '';
        $transaction_id = isset($xmlArr['transaction_id']) ? $xmlArr['transaction_id'] : '';
        $total_fee = isset($xmlArr['total_fee']) ? floatval($xmlArr['total_fee']) / 100 : 0; // 转换为元
        
        //处理返回结果
        if ($return_code == 'SUCCESS' && $result_code == 'SUCCESS') {
            //查询充值记录
            $recharge_record = $CMysql->getRow('fzh_recharge_record', array('recharge_id' => $out_trade_no));
            if(empty($recharge_record)){
                echo 'FAIL';
                exit;
            }
            
            //检查是否已经处理过
            if ($recharge_record['pay_status'] != 1) {
                echo 'SUCCESS';
                exit;
            }
            
            //验证金额
            if ($total_fee != $recharge_record['recharge_amount']) {
                echo 'FAIL';
                exit;
            }
            
            //开启事务
            $CMysql->begin();
            try {
                //更新充值记录状态
                $updateData = array(
                    'pay_status' => 2, // 支付成功
                    'transaction_id' => $transaction_id,
                    'pay_time' => $time,
                    'update_time' => $time
                );
                $CMysql->updateRow('fzh_recharge_record', $updateData, array('recharge_id' => $out_trade_no));
                
                //获取用户当前余额
                $user_id = $recharge_record['user_id'];
                $userRow = $CMysql->getRow('fzh_user', array('user_id' => $user_id));
                if(empty($userRow)){
                    throw new Exception('用户不存在');
                }
                
                $balance_before = isset($userRow['balance']) ? floatval($userRow['balance']) : 0.00;
                $balance_after = $balance_before + $recharge_record['recharge_amount'];
                
                //更新用户余额
                $CMysql->updateRow('fzh_user', 
                    array('balance' => $balance_after), 
                    array('user_id' => $user_id)
                );
                
                //记录余额变动日志
                $balanceLogAttr = array(
                    'user_id' => $user_id,
                    'change_type' => 1, // 1-充值
                    'change_amount' => $recharge_record['recharge_amount'],
                    'balance_before' => $balance_before,
                    'balance_after' => $balance_after,
                    'related_id' => $out_trade_no,
                    'create_time' => $time,
                    'remark' => '充值成功，充值金额：' . $recharge_record['recharge_amount'] . '元'
                );
                $CMysql->addRow('fzh_balance_log', $balanceLogAttr);
                
                //记录充值支付日志
                $payLogAttr = array(
                    'pay_id' => 1,
                    'order_id' => $out_trade_no,
                    'transaction_id' => $transaction_id,
                    'pay_amount' => $total_fee,
                    'code' => $result_code,
                    'response' => json_encode($xmlArr),
                    'create_time' => $time,
                );
                $CMysql->addRow('fzh_notify_paylog', $payLogAttr);
                
                $CMysql->commit();
                echo 'SUCCESS';
                exit;
                
            } catch (Exception $e) {
                $CMysql->rollback();
                echo 'FAIL';
                exit;
            }
        } else {
            //支付失败，更新充值记录状态
            if(!empty($out_trade_no)){
                $updateData = array(
                    'pay_status' => 3, // 支付失败
                    'update_time' => $time,
                    'remark' => '支付失败：' . (isset($xmlArr['err_code_des']) ? $xmlArr['err_code_des'] : '未知错误')
                );
                $CMysql->updateRow('fzh_recharge_record', $updateData, array('recharge_id' => $out_trade_no));
            }
            echo 'FAIL';
            exit;
        }
    }
}
