<?php

/**
 * 订单列表
 * <AUTHOR>
 */

class OrderList extends BaseController
{
    public function run()
    {
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //提取码
        if (!isset($body['fetch_code']) && empty($body['fetch_code'])) {
            $this->render(400, 'error', "Fetch code cannot be empty");
        }
        $fetch_code = $body['fetch_code'];

        //语言
        $lang = !empty($body['lang']) ? $body['lang'] : 'cn';

        //订单状态
        $order_status = isset($body['order_status']) ? $body['order_status'] : 0;

        //1.页码
        $pageNo = 1;
        if (isset($body['pageNo']) and !empty($body['pageNo'])) {
            $pageNo = $body['pageNo'];
        }
        //2.一页的数量
        $pageSize = 10;
        if (isset($body['pageSize']) and !empty($body['pageSize'])) {
            $pageSize = $body['pageSize'];
        }

        $limit_from = ($pageNo - 1) * $pageSize;

        $cond = "t.del = 0 and t.user_id = $user_id and t.fetch_code = '$fetch_code'";
        if ($order_status > 0) {
            $cond .= " and order_status = $order_status";
        }

        //订单数量统计
        $count_cond = "t.del = 0 and t.user_id = $user_id and t.fetch_code = '$fetch_code'";
        //订单总数
        $sql = "select count(*) as num from dv_order t where $count_cond and order_status not in (11,19)";
        $order_total = $CMysql->getRowBySql($sql)['num'];
        //待付款订单总数
        $sql = "select count(*) as num from dv_order t where $count_cond and order_status = 1";
        $order_payment_total = $CMysql->getRowBySql($sql)['num'];
        //制作中订单总数
        $sql = "select count(*) as num from dv_order t where $count_cond and order_status = 4";
        $order_product_total = $CMysql->getRowBySql($sql)['num'];
        //待收货订单总数
        $sql = "select count(*) as num from dv_order t where $count_cond and order_status = 6";
        $order_take_total = $CMysql->getRowBySql($sql)['num'];

        $list = [];

        //语言
        if ($lang == 'cn') {
            $field_b = "xp.subtitle_img_cn as subtitle_img,name_cn as product_name";
            $field_c = "xoi.package_name_cn as package_name,xoi.product_name_cn as product_name,xoi.sku_name_cn as sku_name";
        } else {
            $field_b = "xp.subtitle_img_en as subtitle_img,name_en as product_name";
            $field_c = "xoi.package_name_en as package_name,xoi.product_name_en as product_name,xoi.sku_name_en as sku_name";
        }
        $field_b = "xp.is_alone,xp.price,xp.img_url," . $field_b;
        $field_c = "xoi.item_id,xoi.unit_price as price,xoi.count,xp2.img_url,xp2.is_alone,xoi.sku_id," . $field_c;

        //用户订单列表信息
        $field = "t.order_id,t.order_amount,t.goods_amount,t.order_status,t.pay_status,t.created_at,t.express_sn,t.order_from";
        $sql = "select $field from dv_order t 
                where $cond order by t.created_at desc limit $limit_from, $pageSize;";
        $orderRows = $CMysql->getRowsBySql($sql);
        if (!empty($orderRows)) {
            foreach ($orderRows as $k => $v) {
                $order_id = $v['order_id'];
                $detailAttr = [];

                //套餐
                if ($v['order_from'] == 1) {
                    //查询订单item
                    $sql = "select package_id from dv_order_item"
                        . " where order_id = '$order_id'";
                    $package_id = $CMysql->getRowBySql($sql)['package_id'];

                    $sql = "select $field_b from dv_product xp where product_id = '$package_id'";
                    $productRow = $CMysql->getRowBySql($sql);

                    $itemRow['count'] = 1;
                    $itemRow['img_url'] = $productRow['img_url'] ? $config['img_host'] . 'product/' . $productRow['img_url'] : '';
                    $itemRow['price'] = $v['order_amount'];
                    $itemRow['old_price'] = $v['goods_amount'];
                    $itemRow['product_name'] = $productRow['product_name'];
                    $itemRow['is_alone'] = $productRow['is_alone'];
                    array_push($detailAttr, $itemRow);
                } else {
                    //获取order_item信息
                    $sql = "select $field_c from dv_order_item xoi "
                        . "left join dv_product xp on xp.product_id = xoi.product_id "
                        . "left join dv_product xp2 on xp2.product_id = xoi.package_id "
                        . "where xoi.order_id = '$order_id' order by xoi.item_id asc";
                    $item = $CMysql->getRowsBySql($sql);
                    if (!empty($item)) {
                        foreach ($item as $ka => $va) {
                            $order_item_id = $va['item_id'];

                            //产品对应的照片
                            $sql = "select * from dv_order_pic where item_id = $order_item_id and order_id = '$order_id' and fetch_code = '$fetch_code'";
                            $picRow = $CMysql->getRowBySql($sql);
                            if (!empty($picRow)) {
                                $pic_name = $picRow['pic_name'];
                                $img_url = $config['picture_host'] . $picRow['pic_dir'] . '?time=' . time();
                            } else {
                                $pic_name = '';
                                $img_url =  $va['img_url'] ? $config['img_host'] . 'product/' . $va['img_url'] : '';
                            }
                            //产品图片
                            $item[$ka]['img_url'] = $img_url;
                            $item[$ka]['pic_name'] = $pic_name;
                            $item[$ka]['old_price'] = $va['price'];
                            $item[$ka]['sku_name'] = $va['sku_name'];
                            array_push($detailAttr, $item[$ka]);
                        }
                    }
                }

                //物流信息
                $sql = "select * from dv_order_express where order_id = '$order_id'";
                $expressRows = $CMysql->getRowsBySql($sql);
                $express_count = count($expressRows);
                $express_sn = !empty($expressRows[0]['express_sn']) ? $expressRows[0]['express_sn'] : '';

                $list[] = array(
                    'order_id' => $v['order_id'],
                    'order_status' => $v['order_status'],
                    'pay_status' => $v['pay_status'],
                    'order_amount' => $v['order_amount'],
                    'created_at' => date('Y-m-d H:i:s', $v['created_at']),
                    'express_count' => $express_count,
                    'express_sn' => $express_sn,
                    'detail' => $detailAttr,
                );
            }
        }
        $resp = array(
            'order_total' => $order_total,
            'order_payment_total' => $order_payment_total,
            'order_product_total' => $order_product_total,
            'order_take_total' => $order_take_total,
            'order_list' => $list
        );
        $this->render(200, 'success', $resp);
    }
}
