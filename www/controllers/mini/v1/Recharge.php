<?php

/**
 * Recharge 充值接口
 * <AUTHOR>
 */
class Recharge extends BaseController {

    public function run() {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];
        $open_id = $user['openid'];

        //验证充值金额
        if(!isset($body['amount']) || empty($body['amount']) || $body['amount'] <= 0){
            $this->render(400, 'error', "充值金额不能为空且必须大于0");
        }

        $recharge_amount = floatval($body['amount']);

        //验证充值金额范围（最小1元，最大10000元）
        if($recharge_amount < 1 || $recharge_amount > 10000){
            $this->render(400, 'error', "充值金额必须在1-10000元之间");
        }

        //开启事务
        $CMysql->begin();
        try {
            //生成充值订单号
            $recharge_id = Common::getUUID('CZ');

            //创建充值记录
            $rechargeAttr = array(
                'recharge_id' => $recharge_id,
                'user_id' => $user_id,
                'recharge_amount' => $recharge_amount,
                'pay_amount' => $recharge_amount,
                'pay_type' => 1, // 1-微信支付
                'pay_status' => 1, // 1-待支付
                'create_time' => $time,
                'update_time' => $time,
                'remark' => '用户充值'
            );

            $result = $CMysql->addRow('fzh_recharge_record', $rechargeAttr);
            if(!$result){
                throw new Exception('创建充值记录失败');
            }

            $CMysql->commit();

            //使用微信支付公共类
            $wechatPay = new WechatPay($config['wx_small_config']);
            $notify_url = isset($config['wx_small_config']['recharge_notify']) ?
                         $config['wx_small_config']['recharge_notify'] :
                         $config['wx_small_config']['notify'];

            $payment_params = $wechatPay->createOrder('账户充值', $recharge_id, $recharge_amount, $open_id, $notify_url);

            $this->render('200', 'success', $payment_params);

        } catch (Exception $e) {
            $CMysql->rollback();
            $this->render(400, 'error', '充值失败：' . $e->getMessage());
        }
    }

}
