<?php

/**
 * Recharge 充值接口
 * <AUTHOR>
 */
class Recharge extends BaseController {

    public function run() {
        $time = time();
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        } 
        
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];
        $open_id = $user['openid'];

        //验证充值金额
        if(!isset($body['amount']) || empty($body['amount']) || $body['amount'] <= 0){
            $this->render(400, 'error', "充值金额不能为空且必须大于0");
        }
        
        $recharge_amount = floatval($body['amount']);
        
        //验证充值金额范围（最小1元，最大10000元）
        if($recharge_amount < 1 || $recharge_amount > 10000){
            $this->render(400, 'error', "充值金额必须在1-10000元之间");
        }

        //开启事务
        $CMysql->begin();
        try {
            //生成充值订单号
            $recharge_id = Common::getUUID('CZ');
            
            //创建充值记录
            $rechargeAttr = array(
                'recharge_id' => $recharge_id,
                'user_id' => $user_id,
                'recharge_amount' => $recharge_amount,
                'pay_amount' => $recharge_amount,
                'pay_type' => 1, // 1-微信支付
                'pay_status' => 1, // 1-待支付
                'create_time' => $time,
                'update_time' => $time,
                'remark' => '用户充值'
            );
            
            $result = $CMysql->addRow('fzh_recharge_record', $rechargeAttr);
            if(!$result){
                throw new Exception('创建充值记录失败');
            }
            
            $CMysql->commit();
            
            //调用微信支付
            $total = $recharge_amount * 100; // 转换为分
            $wxcon = $config['wx_small_config'];
            $this->wxpay($wxcon, '账户充值', $recharge_id, $total, $open_id);
            
        } catch (Exception $e) {
            $CMysql->rollback();
            $this->render(400, 'error', '充值失败：' . $e->getMessage());
        }
    }

    /**
     * 微信支付
     */
    private function wxpay($config, $product_name, $orderno, $total, $openid) {
        $time = time();
        // get prepay id
        $prepay_id = $this->generatePrepayId($config, $product_name, $orderno, $total, $openid);
        if(!$prepay_id){
            $this->render(400, 'error', "The prepay_id is error!");
        }
        // re-sign it
        $response = array(
            'appId' => $config['wx_app_id'],
            'package' => "prepay_id=$prepay_id",
            'signType' => "MD5",
            'nonceStr' => $this->generateNonce(),
            'timeStamp' => "$time",
        );
        $response['paySign'] = $this->calculateSign($response, $config['wx_app_key']);
        // send it to APP
        $this->render('200', 'success', $response);
    }

    /**
     * Generate a prepay id
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=9_1
     */
    function generatePrepayId($config, $product_name, $orderno, $total, $openid) {
        $params = array(
            'appid' => $config['wx_app_id'],
            'mch_id' => $config['wx_mch_id'],
            'nonce_str' => $this->generateNonce(),
            'body' => $product_name,
            'out_trade_no' => $orderno,
            'total_fee' => $total,
            'spbill_create_ip' => $_SERVER["REMOTE_ADDR"],
            'notify_url' => $config['recharge_notify'], // 充值回调地址
            'trade_type' => 'JSAPI',
            'openid' => $openid,
        );
        // add sign
        $params['sign'] = $this->calculateSign($params, $config['wx_app_key']);
        // create xml
        $xml = $this->getXMLFromArray($params);
        // send request
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => "https://api.mch.weixin.qq.com/pay/unifiedorder",
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => array('Content-Type: text/xml'),
            CURLOPT_POSTFIELDS => $xml,
        ));
        $result = curl_exec($ch);
        curl_close($ch);
        // get the prepay id from response
        $xml = simplexml_load_string($result);
        if ((string) $xml->return_code = 'SUCCESS') {
            return (string) $xml->prepay_id;
        } else {
            return false;
        }
    }

    /**
     * Calculate sign for WeChat pay
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=4_3
     */
    function calculateSign($params, $key) {
        ksort($params);
        $string = '';
        foreach ($params as $k => $v) {
            if ($v) {
                $string .= $k . '=' . $v . '&';
            }
        }
        $string = $string . 'key=' . $key;
        return strtoupper(md5($string));
    }

    /**
     * Get XML from array
     */
    function getXMLFromArray($params) {
        $xml = '<xml>';
        foreach ($params as $key => $value) {
            $xml .= '<' . $key . '>' . $value . '</' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }

    /**
     * Generate a nonce string
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=4_3
     */
    function generateNonce() {
        return md5(uniqid('', true));
    }
}
