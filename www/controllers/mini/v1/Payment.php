<?php

/**
 * Payment  //小程序支付
 * <AUTHOR>
 */
class Payment extends BaseController {

    public function run() {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];
        $open_id = $user['openid'];

        //获取订单信息
        $order_id = $body['order_id'];
        $orderRow = $CMysql->getRow("fzh_order",array('order_id'=>$order_id));
        if(empty($orderRow)){
            $this->render(400, 'error', "订单信息有误！");
        }

        //验证订单用户
        if($orderRow['user_id'] != $user_id){
            $this->render(400, 'error', "订单用户不匹配！");
        }

        //获取支付方式参数，默认为微信支付
        $pay_type = isset($body['pay_type']) ? intval($body['pay_type']) : 1;
        // 1-微信支付，2-余额支付

        if($pay_type == 2){
            //余额支付（支持商品订单和充值订单）
            $this->balancePay($CMysql, $user_id, $order_id, $orderRow, $time);
        } else {
            //微信支付（支持商品订单和充值订单，统一回调地址）
            try {
                $wechatPay = new WechatPay($config['wx_small_config']);

                // 根据订单类型选择商品名称
                $product_name = ($orderRow['order_type'] == 3) ? '账户充值' : '订单支付';

                // 统一使用WXNotify回调地址
                $payment_params = $wechatPay->createOrder($product_name, $order_id, $orderRow['order_amount'], $open_id, $config['wx_small_config']['notify']);
                $this->render('200', 'success', $payment_params);
            } catch (Exception $e) {
                $this->render(400, 'error', '支付失败：' . $e->getMessage());
            }
        }
    }

    /**
     * 余额支付处理
     */
    private function balancePay($CMysql, $user_id, $order_id, $orderRow, $time) {
        //验证订单状态
        if($orderRow['order_status'] != 1){
            $this->render(400, 'error', "订单状态不正确，无法支付！");
        }

        $pay_amount = floatval($orderRow['order_amount']);

        //获取用户余额
        $userRow = $CMysql->getRow('fzh_user', array('user_id' => $user_id));
        if(empty($userRow)){
            $this->render(400, 'error', "用户信息有误！");
        }

        $user_balance = isset($userRow['balance']) ? floatval($userRow['balance']) : 0.00;

        //验证余额是否足够
        if($user_balance < $pay_amount){
            $this->render(400, 'error', "余额不足！当前余额：" . sprintf("%.2f", $user_balance) . "元，需要支付：" . sprintf("%.2f", $pay_amount) . "元");
        }

        //开启事务
        $CMysql->begin();
        try {
            //生成支付记录ID
            $pay_id = Common::getUUID('YE');

            //计算支付后余额
            $balance_after = $user_balance - $pay_amount;

            //更新用户余额
            $CMysql->updateRow('fzh_user',
                array('balance' => $balance_after),
                array('user_id' => $user_id)
            );

            //更新订单状态
            $orderUpdateData = array(
                'order_status' => 5, // 已付款
                'pay_id' => 2, // 2-余额支付
                'pay_status' => 5, // 已付款
                'pay_time' => $time,
                'update_time' => $time
            );
            $CMysql->updateRow('fzh_order', $orderUpdateData, array('order_id' => $order_id));

            //创建余额支付记录
            $balancePayAttr = array(
                'pay_id' => $pay_id,
                'user_id' => $user_id,
                'order_id' => $order_id,
                'pay_amount' => $pay_amount,
                'balance_before' => $user_balance,
                'balance_after' => $balance_after,
                'pay_status' => 1, // 支付成功
                'create_time' => $time,
                'update_time' => $time,
                'remark' => '余额支付订单：' . $order_id
            );
            $CMysql->addRow('fzh_balance_pay_record', $balancePayAttr);

            //记录余额变动日志
            $balanceLogAttr = array(
                'user_id' => $user_id,
                'change_type' => 2, // 2-支付
                'change_amount' => $pay_amount,
                'balance_before' => $user_balance,
                'balance_after' => $balance_after,
                'related_id' => $order_id,
                'create_time' => $time,
                'remark' => '余额支付订单，支付金额：' . $pay_amount . '元'
            );
            $CMysql->addRow('fzh_balance_log', $balanceLogAttr);

            //订单日志
            $logAttr = array(
                'order_id' => $order_id,
                'desc' => "订单通过余额付款，付款金额：" . $pay_amount . "元",
                'create_time' => $time,
                'info_before' => '待付款',
                'info_after' => '已付款',
                'is_show' => 1,
                'operator_id' => $user_id,
                'operator_name' => 'User',
            );
            $CMysql->addRow('fzh_order_log', $logAttr);

            $CMysql->commit();

            //返回支付成功信息
            $resp = array(
                'pay_id' => $pay_id,
                'order_id' => $order_id,
                'pay_amount' => sprintf("%.2f", $pay_amount),
                'balance_after' => sprintf("%.2f", $balance_after),
                'pay_time' => date('Y-m-d H:i:s', $time),
                'pay_type' => 'balance'
            );

            $this->render('200', '支付成功', $resp);

        } catch (Exception $e) {
            $CMysql->rollback();
            $this->render(400, 'error', '支付失败：' . $e->getMessage());
        }
    }
}
