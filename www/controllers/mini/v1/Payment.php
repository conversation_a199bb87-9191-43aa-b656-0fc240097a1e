<?php

/**
 * Payment  //小程序支付
 * <AUTHOR>
 */
class Payment extends BaseController {

    public function run() {
        $time = time();
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        } 
        
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $open_id = $user['openid'];
    
        //获取订单信息
        $order_id = $body['order_id'];
        $orderRow = $CMysql->getRow("fzh_order",array('order_id'=>$order_id));
        if(empty($orderRow)){
            $this->render(400, 'error', "订单信息有误！");
        }
        
        $total = $orderRow['order_amount'] * 100;

        //读取支付配置
        $wxcon = $config['wx_small_config'];
        $this->wxpay($wxcon, '支付', $order_id, $total,$open_id);
    }

    /**
     * 微信支付
     */
    private function wxpay($config, $product_name, $orderno, $total,$openid) {
        $time = time();
        // get prepay id
        $prepay_id = $this->generatePrepayId($config, $product_name, $orderno, $total,$openid);
        if(!$prepay_id){
            $this->render(400, 'error', "The prepay_id is error!");
        }
        // re-sign it
        $response = array(
            'appId' => $config['wx_app_id'],
            'package' => "prepay_id=$prepay_id",
            'signType' => "MD5",
            'nonceStr' => $this->generateNonce(),
            'timeStamp' => "$time",
        );
        $response['paySign'] = $this->calculateSign($response, $config['wx_app_key']);
        // send it to APP
        $this->render('200', 'success', $response);
    }

    /**
     * Generate a prepay id
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=9_1
     */
    function generatePrepayId($config, $product_name, $orderno, $total,$openid) {
        $params = array(
            'appid' => $config['wx_app_id'],
            'mch_id' => $config['wx_mch_id'],
            'nonce_str' => $this->generateNonce(),
            'body' => $product_name,
            'out_trade_no' => $orderno,
            'total_fee' => $total,
            'spbill_create_ip' => $_SERVER["REMOTE_ADDR"],
            'notify_url' => $config['notify'],
            'trade_type' => 'JSAPI',
            'openid' => $openid,
        );
        // add sign
        $params['sign'] = $this->calculateSign($params, $config['wx_app_key']);
        // create xml
        $xml = $this->getXMLFromArray($params);
        // send request
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => "https://api.mch.weixin.qq.com/pay/unifiedorder",
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => array('Content-Type: text/xml'),
            CURLOPT_POSTFIELDS => $xml,
        ));
        $result = curl_exec($ch);
        curl_close($ch);
        // get the prepay id from response
        $xml = simplexml_load_string($result);
        //var_dump($xml->return_code.','.$xml->return_msg);die;
        if ((string) $xml->return_code = 'SUCCESS') {
            return (string) $xml->prepay_id;
        } else {
            return false;
        }
    }

    /**
     * Get a sign string from array using app key
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=4_3
     */
    function calculateSign($arr, $key) {
        ksort($arr);

        $buff = "";
        foreach ($arr as $k => $v) {
            if ($k != "sign" && $k != "key" && $v != "" && !is_array($v)) {
                $buff .= $k . "=" . $v . "&";
            }
        }

        $buff = trim($buff, "&");

        return strtoupper(md5($buff . "&key=" . $key));
    }

    /**
     * Get xml from array
     */
    function getXMLFromArray($arr) {
        $xml = "<xml>";
        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= sprintf("<%s>%s</%s>", $key, $val, $key);
            } else {
                $xml .= sprintf("<%s><![CDATA[%s]]></%s>", $key, $val, $key);
            }
        }

        $xml .= "</xml>";
        return $xml;
    }

    /**
     * Generate a nonce string
     *
     * @link https://pay.weixin.qq.com/wiki/doc/api/app.php?chapter=4_3
     */
    function generateNonce() {
        return md5(uniqid('', true));
    }

}
