<?php

/**
 * 下单接口
 * <AUTHOR>
 */
class OrderCreate extends BaseController
{
    public function run()
    {
        $time = time();
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        } 
        
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        $customer_user_id = $user_id;
        //获取最新的用户信息
        $condition = array('user_id' => $user['user_id']);
        $user_info = $CMysql->getRow('dv_user', $condition);
        //如果是官方账号，代客下单
        if($user_info['is_official'] == 1){
            //客户手机号,如果未填写客户手机号，视为官方账号本身下单
            $user_tel = isset($body['user_tel']) ? $body['user_tel'] : '';
            if(!empty($user_tel)){
                //验证当前手机号是否存在
                $map['login_name'] = $user_tel;
                $userRow = $CMysql->getRow('dv_user', $map);
                if(empty($userRow)){
                    //注册用户
                    $userAttr['login_name'] = $user_tel;
                    $userAttr['user_tel'] = $user_tel;
                    $userAttr['created_at'] = $time;
                    $userAttr['updated_at'] = $time;
                    $customer_user_id = $CMysql->addRow('dv_user', $userAttr);
                }else{
                    $customer_user_id = $userRow['user_id'];
                }
            }
        }

        //商品参数
        if(!isset($body['items']) || empty($body['items'])){
            $this->render(401, 'error', "Product cannot be empty");
        }
        
        //提取码
        if(!isset($body['fetch_code']) || empty($body['fetch_code'])){
            $this->render(401, 'error', "Fetch_code cannot be empty");
        }

        //创建订单
        $data['user_id'] = $user_id;
        $data['customer_user_id'] = $customer_user_id;
        $data['login_name'] = $user['login_name'];
        $Order = x5()->registerModel('Order', $data);
        $result = $Order->createOrder($body);
        if(!empty($result)){
            if($result['code'] == '200'){
                $order_id = $result['info'];
                $omap['order_id'] = $order_id;
                $orderAttr = $CMysql->getRow('dv_order',$omap);
                if(!empty($orderAttr)){
                    //成功以后的返回
                    $resp = array(
                        'order_id' => $order_id,
                    );
                }
                $this->render(200, "success", $resp);
            }else{
                $this->render(400, "error", $result['info']);
            }
        }else{
            $this->render(400, "error", "下单失败");
        }
    }
}
