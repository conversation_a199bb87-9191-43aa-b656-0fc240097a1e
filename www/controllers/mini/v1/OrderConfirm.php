<?php

/**
 * 订单确认接口
 * <AUTHOR>
 */
class OrderConfirm extends BaseController {

    public function run() {
        $time = time();
        $resp = [];
        $cartAttr = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        } 
        
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //下单来源 1、套餐，商品套餐列表点+号，或者套餐详情点购买，直接下单
        $order_from = isset($body['order_from']) ? $body['order_from'] : 0;

        //商品参数
        if(!isset($body['items']) || empty($body['items'])){
            $this->render(401, 'error', "Product cannot be empty");
        } 
        $items = $body['items'];

        //提取码
        if(!isset($body['fetch_code']) || empty($body['fetch_code'])){
            $this->render(401, 'error', "Fetch_code cannot be empty");
        }
        $fetch_code = $body['fetch_code'];

        //语言
        $lang = !empty($body['lang']) ? $body['lang'] : 'cn';

        //语言
        if($lang == 'cn'){
            $field_a = "subtitle_img_cn as subtitle_img,name_cn as product_name";
            $field_c = "xp.name_cn as name,xs.sku_name_cn as sku_name";
        }else{
            $field_a = "subtitle_img_en as subtitle_img,name_en as product_name";
            $field_c = "xp.name_en as name,xs.sku_name_en as sku_name";
        }
        $field_a = "product_id,img_url,price as shop_price,early_bird_price,".$field_a;
        $field_c = "xp.product_id,xp.status as product_status,xp.img_url,xp.is_package,xs.sku_id,xs.shop_price,xs.status as sku_status,".$field_c;

        //套餐立即购买，不走购物车
        if($order_from == 1){
            //套餐
            $sql = "select $field_a from dv_product where product_id = $items";
            $dataRow = $CMysql->getRowBySql($sql);

            //查询套餐选片详情
            if($lang == 'cn'){
                $field_b = "any_value(t2.name_cn) as product_name";
            }else{
                $field_b = "any_value(t2.name_en) as product_name";
            }
            $field_b = "any_value(t1.package_pid) package_pid,any_value(t1.package_sku) package_sku,".$field_b;
            $sql = "select $field_b from dv_package_pic t1"
                ." left join dv_product t2 on t1.package_pid = t2.product_id"
                ." where t1.user_id = $user_id and t1.fetch_code = '$fetch_code' and t1.product_id = $items and t1.del = 0 group by any_value(t1.package_pid) order by any_value(t1.add_time) asc";
            $itemAttr = $CMysql->getRowsBySql($sql);
            if(!empty($itemAttr)){
                foreach ($itemAttr as $key => $value) {
                    $package_pid = $value['package_pid'];
                    $package_sku = $value['package_sku'];
                    //产品对应的照片
                    $sql = "select pic_id,pic_name,pic_dir from dv_package_pic where user_id = $user_id and fetch_code = '$fetch_code' and product_id = '$items' and package_pid = $package_pid and package_sku = $package_sku and del = 0 order by pic_name asc";
                    $picRows = $CMysql->getRowsBySql($sql);
                    if(!empty($picRows)){
                        foreach ($picRows as $k => $val) {
                            $picRows[$k]['pic_dir'] = $config['picture_host'].$val['pic_dir'] . '?time='.$time;
                        }
                    }
                    $itemAttr[$key]['picRows'] = $picRows;
                }
            }

            //如果在早鸟价期间，展示早鸟价
            $is_early_bird = Common::is_early_bird($fetch_code);
            //展示正常价格
            $shop_price = $dataRow['shop_price'];
            //展示早鸟价
            if($is_early_bird == 1){
                $shop_price = $dataRow['early_bird_price'];
            }
            //产品数据
            $productRows[] = array(
                'product_id' => $items,
                'name' => $dataRow['product_name'],
                'img_url' => $dataRow['img_url'] ? $config['img_host'].'product/'.$dataRow['img_url'] : '',
                'subtitle_img' => $dataRow['subtitle_img'] ? $config['img_host'].'product/'.$dataRow['subtitle_img'] : '',
                'shop_price' => $shop_price,
                'old_price' => $dataRow['shop_price'],
                'count' => 1,
                'picRows' => $itemAttr
            );
            $total_price = $shop_price;
            $total_count = 1;
            $product_prices = $dataRow['shop_price'];
            $skus = $items;
            $cart_items = [];
            $sku_pics = [];
        }else{
            //查询购物车
            $condition_cart = array('status' => 1, 'user_id' => $user_id,'fetch_code'=>$fetch_code);
            $cartInfo = $CMysql->getRow('dv_es_cart', $condition_cart);
            if(empty($cartInfo) || ($cartInfo['total_count'] == 0 && $cartInfo['item_count'] == 0)){
                $this->render('400', 'fail','购物车为空');
            }
            $cart_id = $cartInfo['cart_id'];
            $cart_items = json_decode($cartInfo['items'], true);
            if(empty($cart_items)){
                $this->render('400', 'error', "商品不存在或者已下架!");
            }

            //从购物车中取出需要下单的产品
            foreach ($items as $key => $data_id) {
                $cartAttr[$data_id] = $cart_items[$data_id];
            }

            if(empty($cartAttr)){
                $this->render('400', 'error', "商品不存在或者已下架!");
            }

            //计算总价和总数量
            $total_price = 0.0;
            $total_count = 0;
            $skus = array();
            foreach ($cartAttr as $data_id => $item) {
                //总数
                $total_count += $item['count'];
                //组织产品数据
                $skuAttr = [];
                $pic_id = $item['pic_id'];
                $sku_id = explode('_',$data_id)[0];
                
                //单品
                $sql = "select $field_c from dv_product xp "
                    . "left join dv_product_sku xs on xs.product_id = xp.product_id "
                    . "where xs.sku_id = $sku_id";
                $dataRow = $CMysql->getRowBySql($sql);
                if($dataRow['product_status'] == 1 && $dataRow['sku_status'] == 1){
                    $total_price += ($dataRow['shop_price'] * $item['count']);
                }
                //属性
                $skuAttr[] = array(
                    'sku_id' => $dataRow['sku_id'],
                    'sku_name' => $dataRow['sku_name'],
                    'package_id' => $item['group_id'],
                );

                array_push($skus, $sku_id);
                $sku_pics[$sku_id] = $pic_id;
                $product_id = $dataRow['product_id'];

                $img_url = '';
                $pic_name = '';
                if(!empty($pic_id)){
                    //查询图片信息
                    $sql = "select name,dir from dv_pic where id = $pic_id";
                    $picRow = $CMysql->getRowBySql($sql);
                    if(!empty($picRow)){
                        $img_url = $config['picture_host'].$picRow['dir'] . '?time='.$time;
                        $pic_name = $picRow['name'];
                    }
                }else{
                    //没有选择的照片，就展示商品图片
                    $img_url = $config['img_host'].'product/'.$dataRow['img_url'];
                    $pic_name = $dataRow['name'];
                }

                //产品数据
                $productRows[] = array(
                    'product_id' => $product_id,
                    'name' => $dataRow['name'],
                    'pic_name' => $pic_name,
                    'img_url' => $img_url,
                    'is_package' => $dataRow['is_package'],
                    'shop_price' => $dataRow['shop_price'],
                    'count' => $item['count'],
                    'skuAttr' => $skuAttr
                );
            }
            $product_prices = $total_price;
        }

        //确认购物车是否存在有效商品
        if (empty($productRows)) {
            $this->render('400', 'error', "商品不存在或者已下架!");
        }

        //获取可用优惠券
        $coupons = $this->getUserCoupon($CMysql,$user_id,$skus,$product_prices,$order_from,$cart_items,$sku_pics,$lang);

        //邮费+包邮价格
        $school_id = Common::getFetchCodeToSchool($fetch_code);
        $sql = "select postage,free_price from dv_school where id = $school_id";
        $postage = $CMysql->getRowBySql($sql);

        $resp['productRows'] = $productRows;
        //$resp['items'] = $cartAttr;
        $resp['total_count'] = intval($total_count);
        $resp['total_price'] = sprintf("%.2f",$total_price);
        $resp['product_prices'] = sprintf("%.2f",$product_prices);
        $resp['coupons'] = array_values($coupons);
        $resp['address'] = $this->getUserAddress($CMysql,$user_id);
        $resp['frame'] = Common::getFrame($fetch_code);
        $resp['postage'] = $postage;
        $this->render('200', 'success', $resp);
    }

    /**
     * 获取默认地址
     * @param  [type] $CMysql  [description]
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    private function getUserAddress($CMysql,$user_id)
    {
        //查询默认收货地址
        $sql = "select xua.province_id,xua.city_id,xua.area_id,xua.address_id,xua.consignee,xua.consignee_tel,xua.address,xua.email,xua.is_default,xua.remark,xap.area_name as province_name,xac.area_name as city_name,xaa.area_name as area_name from dv_user_address xua"
                . " left join dv_area xap on (xap.id = xua.province_id)"
                . " left join dv_area xac on (xac.id = xua.city_id)"
                . " left join dv_area xaa on (xaa.id = xua.area_id)"
                . " where xua.user_id = $user_id and xua.del = 0 and xua.is_default = 1";
        $address = $CMysql->getRowBySql($sql);
        return $address;
    }

    /**
     * 获取可用优惠券
     * @return [type] [description]
     */
    private function getUserCoupon($CMysql,$user_id,$params,$total_price,$order_from,$cart_items = [],$sku_pics = [],$lang)
    {
        $time = time();
        if($lang == 'en'){
            $activity_name = "xa.activity_name_en as activity_name";
        }else{
            $activity_name = "xa.activity_name";
        }
        //可用优惠券
        $coupon_sql = "select xu.*,xc.coupon_name,$activity_name from dv_user_coupon xu left join dv_coupon xc on (xc.coupon_id = xu.coupon_id) left join dv_activity xa on (xa.activity_id = xu.activity_id) where xu.user_id = '$user_id' and xu.status = 0 and case when xu.is_long = 1 then 1=1 when xu.is_long = 2 then $time >= xu.start_time and $time <= xu.end_time end";
        $coupons = $CMysql->getRowsBySql($coupon_sql);
        if(!empty($coupons)){
            foreach ($coupons as $k => $v) {
                $allow = 0;
                $total = 0.00;
                if($v['total']){
                    $coupons[$k]['total'] = floatval($v['total']);
                }
                if($v['reduce']){
                    $coupons[$k]['reduce'] = floatval($v['reduce']);
                }
                if($v['is_long'] == 2){
                    $coupons[$k]['start_time'] = date('Y.m.d', $v['start_time']);
                    $coupons[$k]['end_time'] = date('Y.m.d', $v['end_time']);
                }
                if($v['discount']){
                    $coupons[$k]['discount'] = '-' . (1 - $v['discount']) * 100;
                }
                if($v['range'] == 2){  //指定商品
                    if($order_from == 1){
                        $couponItems = $CMysql->getRows('dv_activity_item',array('activity_id' => $v['activity_id'],'product_id' => $params));
                        if(!empty($couponItems)){
                            $allow = 1;
                            $total = $total_price;
                        }
                    }else{
                        if(is_array($params)){
                            $couponItems = $CMysql->getRows('dv_activity_item',array('activity_id' => $v['activity_id']));
                            foreach ($couponItems as $key => $sku) {
                                $sku_id = $sku['sku_id'];
                                $cart_sku_id = $sku_id;
                                if(array_key_exists($sku_id,$sku_pics)){
                                    $cart_sku_id = $sku_id.'_'.$sku_pics[$sku_id];
                                }
                                if(in_array($sku_id,$params) && $sku['product_id'] == $cart_items[$cart_sku_id]['group_id']){
                                    $allow = 1;
                                    $count = $cart_items[$cart_sku_id]['count'];
                                    $sku_info = $CMysql->getRow('dv_product_sku',array('sku_id' => $sku_id));
                                    $total += ($sku_info['shop_price'] * $count);
                                }
                            }
                        }else{
                            $couponItems = $CMysql->getRows('dv_activity_item',array('activity_id' => $v['activity_id'],'sku_id' => $params));
                            if(!empty($couponItems)){
                                $allow = 1;
                                $total = $total_price;
                            }
                        }
                    }
                }else {    //全品类
                    $allow = 1;
                    $total = $total_price;
                }
                if($allow == 1){
                    if ($v['type'] == 2) { //满减券
                        if ($total < $v['total']) {
                            unset($coupons[$k]);
                        }
                    }
                }else{
                    unset($coupons[$k]);
                }
            }
        }
        return $coupons;
    }
}
