<?php
/**
 * Notify //支付回调
 * <AUTHOR>
 */

class WXNotify extends BaseController
{
    public function run()
    {
        $time = time();
        //加载数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //接收回调参数
        $xml = isset($GLOBALS['HTTP_RAW_POST_DATA']) ? $GLOBALS['HTTP_RAW_POST_DATA'] : file_get_contents("php://input");
        $xmlObj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        $xmlArr = json_decode(json_encode($xmlObj), true);
        //$trade_type=$xmlArr['trade_type'];;
        $out_trade_no = $xmlArr['out_trade_no']; //商户订单号
        //$openid=$xmlArr['openid']; //用户标识
        //$bank_type=$xmlArr['bank_type'];
        $total_fee = $xmlArr['total_fee'] / 100; //订单金额
        $cash_fee  = $xmlArr['cash_fee'] / 100; //实际支付金额
        //$nonce_str=$xmlArr['nonce_str'];
        $transaction_id = $xmlArr['transaction_id']; //支付订单号
        $sign = $xmlArr['sign']; //支付签名
        $result_code = $xmlArr['result_code'];
        //处理返回结果
        if ($result_code == 'SUCCESS') {
            $order_result = $CMysql->getRow('fzh_order', array('order_id' => $out_trade_no));
            if ($order_result['order_status'] != '1') {
                echo 'SUCCESS';
                exit;
            } else {
                if ($total_fee == $order_result['order_amount']) {
                    //更新订单状态
                    $upt_sql = "update fzh_order set order_status = 5, pay_id = 1,pay_status = 5, pay_time = $time, update_time = $time where order_id='$out_trade_no' and order_status = 1 and pay_status = 1";
                    $CMysql->execute($upt_sql);
                    //订单日志
                    $logAttr = array(
                        'order_id'      => $out_trade_no,
                        'desc'          => "订单通过微信付款，付款金额：" . $total_fee,
                        'create_time'    => $time,
                        'info_before'   => '待付款',
                        'info_after'    => '已付款',
                        'is_show'       => 1,
                        'operator_id'   => '100',
                        'operator_name' => 'System',
                    );
                    $CMysql->addRow('fzh_order_log', $logAttr);
                    //支付日志
                    $payarr = array(
                        'pay_id' => 1,
                        'order_id'   => $out_trade_no,
                        'transaction_id'   => $transaction_id,
                        'pay_amount' => $total_fee,
                        'code' => $result_code,
                        'response'   => json_encode($xmlArr),
                        'create_time' => $time,
                    );
                    $CMysql->addRow('fzh_notify_paylog', $payarr);
                    echo 'SUCCESS';
                    exit;
                }
            }
        } else {
            return;
            exit;
        }
    }
}
