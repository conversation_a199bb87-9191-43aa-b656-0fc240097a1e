<?php
/**
 * Notify //支付回调
 * <AUTHOR>
 */

class WXNotify extends BaseController
{
    public function run()
    {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //加载数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化微信支付类
        $wechatPay = new WechatPay($config['wx_small_config']);

        //使用微信支付类处理回调
        $wechatPay->handleNotify(function($data) use ($CMysql, $time) {
            return $this->processOrderNotify($data, $CMysql, $time);
        });
    }

    /**
     * 处理订单支付回调业务逻辑
     */
    private function processOrderNotify($data, $CMysql, $time) {
        $out_trade_no = $data['out_trade_no']; //商户订单号
        $total_fee = $data['total_fee'] / 100; //订单金额
        $transaction_id = $data['transaction_id']; //支付订单号
        $result_code = $data['result_code'];
        //处理返回结果
        if ($result_code == 'SUCCESS') {
            $order_result = $CMysql->getRow('fzh_order', array('order_id' => $out_trade_no));
            if ($order_result['order_status'] != '1') {
                return true;
            } else {
                if ($total_fee == $order_result['order_amount']) {
                    //更新订单状态
                    $upt_sql = "update fzh_order set order_status = 5, pay_id = 1,pay_status = 5, pay_time = $time, update_time = $time where order_id='$out_trade_no' and order_status = 1 and pay_status = 1";
                    $CMysql->execute($upt_sql);
                    //订单日志
                    $logAttr = array(
                        'order_id'      => $out_trade_no,
                        'desc'          => "订单通过微信付款，付款金额：" . $total_fee,
                        'create_time'    => $time,
                        'info_before'   => '待付款',
                        'info_after'    => '已付款',
                        'is_show'       => 1,
                        'operator_id'   => '100',
                        'operator_name' => 'System',
                    );
                    $CMysql->addRow('fzh_order_log', $logAttr);
                    //支付日志
                    $payarr = array(
                        'pay_id' => 1,
                        'order_id'   => $out_trade_no,
                        'transaction_id'   => $transaction_id,
                        'pay_amount' => $total_fee,
                        'code' => $result_code,
                        'response'   => json_encode($data),
                        'create_time' => $time,
                    );
                    $CMysql->addRow('fzh_notify_paylog', $payarr);
                    return true;
                }
            }
        }
        return false;
    }
}
