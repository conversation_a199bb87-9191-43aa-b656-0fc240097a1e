<?php
/**
 * Order Service
 */
class Order
{
    private $user_id = 0;
    private $customer_user_id = 0;
    private $login_name = null;
    private $system_time = null;

	//订单类型 

	/**
     * 初始化
     */
    public function __construct($params)
    {
        $this->user_id = $params['user_id'];
        $this->customer_user_id = $params['customer_user_id'];
        $this->login_name = $params['login_name'];
    	$this->system_time = time();
    }


    /**
	 * 获取实例
	 */
	public static function getInstance()
	{
		//判断该实例是否被实例化
		if(!(self::$ins instanceof self)){
			self::$ins = new self();
		}
		return self::$ins;
	}

	/**
     * 创建订单
     */
    public function createOrder($data = array())
    {
        //实例化主资源类
        $resource = new Resource();
        //redis
        $redis = $resource->getRedis('redis');
        //加载配置
        $config = $resource->getConfig();
        //DB
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);

        //提取码
        $fetch_code = $data['fetch_code'];

        //学校ID
        $sql = "select school_id from dv_pic_folder where fetch_code = '$fetch_code' and level = 2";
        $school_id = $CMysql->getRowBySql($sql)['school_id'];

        //下单来源 1、套餐，商品套餐列表点+号，或者套餐详情点购买，直接下单
        $order_from = isset($data['order_from']) ? $data['order_from'] : 0;

        //开启事务
        $CMysql->begin();
        try {
            //1.生成订单
            $order_id = Common::getUUID('DD');
            $orderAttr = array(
                'order_id' => $order_id,
                'user_id' => $this->customer_user_id,
                'order_status' => 1,
                'order_source' => 1,
                'fetch_code' => $fetch_code,
                'school_id' => !empty($school_id) ? $school_id : 0,
                'is_frame' => !empty($data['is_frame']) ? $data['is_frame'] : 0,
                'order_remark' => isset($data['remark'])?$data['remark']:'',
                'order_from' => $order_from,
                'created_at' => $this->system_time,
                'updated_at' => $this->system_time
            );

            //获取地址信息
            if(isset($data['addr_id'])){
                $addr_id = $data['addr_id'];
                $address_data = $this->getUserAddrById($CMysql,$addr_id,$this->user_id);

                if(empty($address_data)){
                    return array("code"=>'400',"info"=>"收货地址无效");
                }

                if(empty($address_data['email'])){
                    if($data['lang'] == 'cn'){
                        $email_msg = "请补充邮箱后继续下单";
                    }else{
                        $email_msg = "Please fill in your email";
                    }
                    return array("code"=>'400',"info"=>$email_msg);
                }

                $orderAttr['take_type'] = 1;
                $orderAttr['user_address_id'] = $addr_id;
                $orderAttr['consignee'] = $address_data['consignee'];
                $orderAttr['consignee_tel'] = $address_data['consignee_tel'];
                $orderAttr['province_id'] = $address_data['province_id'];
                $orderAttr['city_id'] = $address_data['city_id'];
                $orderAttr['area_id'] = $address_data['area_id'];
                $orderAttr['address'] = $address_data['address'];
                $orderAttr['email'] = $address_data['email'];
            }else{
                return array("code"=>'400',"info"=>"请选择收货地址");
            }

            //2.产品数据，准备生成订单item
            $items = $data['items'];
            if($order_from == 1){
                //套餐
                $sql = "select product_id,name_cn,name_en,status,img_url,price as shop_price,is_package,early_bird_price from dv_product where product_id = $items";
                $dataRow = $CMysql->getRowBySql($sql);

                //如果在早鸟价期间，使用早鸟价
                $is_early_bird = Common::is_early_bird($fetch_code);
                //展示正常价格
                $shop_price = $dataRow['shop_price'];
                //展示早鸟价
                if($is_early_bird == 1){
                    $shop_price = $dataRow['early_bird_price'];
                }
                $goods_amount = $dataRow['shop_price'];
                $total_price = $shop_price;

                //套餐属性
                $field = "t1.sku_id,t1.sku_name_cn,t1.sku_name_en,t1.package_count,t2.name_cn,t2.name_en,t3.sku_name_cn as package_sku_name_cn,t3.sku_name_en as package_sku_name_en,t1.package_pid,t1.package_sku,t1.shop_price,t1.is_photo,t1.range_min,t1.range_max";
                $sql = "select $field from dv_product_sku t1"
                    ." left join dv_product t2 on t1.package_pid = t2.product_id"
                    ." left join dv_product_sku t3 on t1.package_sku = t3.sku_id"
                    ." where t1.product_id = $items and t1.del = 0";
                $skuRows = $CMysql->getRowsBySql($sql);
                foreach ($skuRows as $k => $v) {
                    $attr[$k]['data_id'] = $items;
                    $attr[$k]['sku_id'] = $v['sku_id'];
                    if(empty($v['sku_name_cn'])){
                        $sku_name_cn = $v['name_cn'].'（'.$v['package_sku_name_cn'].'） x '.$v['package_count'];
                    }else{
                        $sku_name_cn = $v['sku_name_cn'];
                    }
                    if(empty($v['sku_name_en'])){
                        $sku_name_en = $v['name_en'].'（'.$v['package_sku_name_en'].'） x '.$v['package_count'];
                    }else{
                        $sku_name_en = $v['sku_name_en'];
                    }
                    $attr[$k]['sku_name_cn'] = $sku_name_cn;
                    $attr[$k]['sku_name_en'] = $sku_name_en;
                    //套餐ID
                    $attr[$k]['package_id'] = $items;
                    $attr[$k]['package_name_cn'] = $dataRow['name_cn'];
                    $attr[$k]['package_name_en'] = $dataRow['name_en'];
                    $attr[$k]['product_id'] = $v['package_pid'];
                    $attr[$k]['package_sku'] = $v['package_sku'];
                    $attr[$k]['product_name_cn'] = $v['name_cn'];
                    $attr[$k]['product_name_en'] = $v['name_en'];
                    $attr[$k]['count'] = $v['package_count'];
                    $attr[$k]['unit_price'] = $v['shop_price'];
                    $attr[$k]['price'] = $v['shop_price'];
                    //需要选片计算选片数量
                    if($v['is_photo'] == 1){
                        $attr[$k]['pic_need'] = $v['package_count'];
                    }else{
                        $attr[$k]['pic_need'] = 0;
                    }
                    $attr[$k]['range_min'] = $v['range_min'];
                    $attr[$k]['range_max'] = $v['range_max'];
                    //属性
                    $skuAttr[] = $attr[$k];
                }
            }else{
                //查询购物车
                $condition_cart = array('status' => 1, 'user_id' => $this->user_id,'fetch_code'=>$fetch_code);
                $cartInfo = $CMysql->getRow('dv_es_cart', $condition_cart);
                if(empty($cartInfo) || ($cartInfo['total_count'] == 0 && $cartInfo['item_count'] == 0)){
                    $this->render('400', 'fail','购物车为空');
                }
                $cart_id = $cartInfo['cart_id'];
                $cart_items = json_decode($cartInfo['items'], true);
                if(empty($cart_items)){
                    $this->render('400', 'error', "商品不存在或者已下架!");
                }

                //从购物车中取出需要下单的产品
                $cartAttr = [];
                foreach ($items as $key => $data_id) {
                    $cartAttr[$data_id] = $cart_items[$data_id];
                }

                //计算总价和总数量
                $total_price = 0.0;
                $skuAttr = [];
                $pic_min = 0;
                $pic_max = 0;
                foreach ($cartAttr as $data_id => $item) {
                    $pic_id = $item['pic_id'];
                    $sku_id = explode('_',$data_id)[0];
                    //单品
                    $sql = "select xp.product_id,xp.name_cn,xp.name_en,xp.status as product_status,xp.img_url,xp.is_package,xs.sku_id,xs.shop_price,xs.status as sku_status,xs.sku_name_cn,xs.sku_name_en,xs.del,xs.is_photo,xs.range_min,xs.range_max from dv_product xp "
                        . "left join dv_product_sku xs on xs.product_id = xp.product_id "
                        . "where xs.sku_id = $sku_id";
                    $dataRow = $CMysql->getRowBySql($sql);
                    if($dataRow['product_status'] == 1 && $dataRow['del'] == 0){
                        $total_price += ($dataRow['shop_price'] * $item['count']);
                    }
                    if($dataRow['is_photo'] == 1){
                        $pic_need = $item['count'];
                    }else{
                        $pic_need = 0;
                    }

                    //需要选片数量计算
                    $range_min = 0;
                    $range_max = 0;
                    $s_product_id = $item['group_id'];
                    $s_package_pid = $dataRow['product_id'];
                    $sql = "select is_photo from dv_product_sku where product_id = $s_product_id and package_pid = $s_package_pid";
                    $is_photo = $CMysql->getRowBySql($sql)['is_photo'];
                    if($is_photo == 1){
                        $range_min = !empty($dataRow['range_min']) ? $dataRow['range_min'] : 1;
                        $range_max = !empty($dataRow['range_max']) ? $dataRow['range_max'] * $item['count'] : $item['count'];
                    }
                    //属性
                    $skuAttr[] = array(
                        'sku_id' => $dataRow['sku_id'],
                        'sku_name_cn' => $dataRow['sku_name_cn'],
                        'sku_name_en' => $dataRow['sku_name_en'],
                        'package_id' => $item['group_id'],
                        'package_name_cn' => '',
                        'package_name_en' => '',
                        'product_id' => $dataRow['product_id'],
                        'product_name_cn' => $dataRow['name_cn'],
                        'product_name_en' => $dataRow['name_en'],
                        'count' => $item['count'],
                        'unit_price' => $dataRow['shop_price'],
                        'price' => $item['count'] * $dataRow['shop_price'],
                        'pic_need' => $pic_need,
                        'range_min' => $range_min,
                        'range_max' => $range_max,
                        'pic_id' => $pic_id
                    );
                    
                }
                $goods_amount = $total_price;
            }
            
            //订单金额
            $orderAttr['goods_amount'] = $goods_amount;
            $orderAttr['order_amount'] = $total_price;

            //优惠券计算
            if (!empty($data['coupon_id'])) {
                $couponResult = $this->_orderCouponPrice($CMysql,$this->user_id,$data['coupon_id'],$goods_amount,$order_id);
                $orderAttr['discount'] = $couponResult['discount'];
                $orderAttr['order_amount'] = $couponResult['order_amount'];
            }

            if($orderAttr['order_amount'] < 0){
                $orderAttr['order_amount'] = 0.00;
            }

            //处理邮费
            $sql = "select postage,free_price from dv_school where id = $school_id";
            $postageRow = $CMysql->getRowBySql($sql);
            //邮费大于0
            if($postageRow['postage'] > 0){
                //订单金额小于包邮价格，计算邮费
                if($orderAttr['order_amount'] < $postageRow['free_price']){
                    //订单金额 + 邮费
                    $orderAttr['order_amount'] = $orderAttr['order_amount'] + $postageRow['postage'];
                    $orderAttr['postage'] = $postageRow['postage'];
                    $orderAttr['is_postfree'] = 1;
                }
            }

            $n = $CMysql->addRow('dv_order', $orderAttr);

            $log_data = array();

            //2：格式化购物车数据，依次生成订单详情
            foreach ($skuAttr as $key => $val) {
                $sku_id = $val['sku_id'];
                
                $item_data = array(
                    'order_id' => $order_id,
                    'package_id' => $val['package_id'],
                    'package_name_cn' => $val['package_name_cn'],
                    'package_name_en' => $val['package_name_en'],
                    'product_id' => $val['product_id'],
                    'product_name_cn' => $val['product_name_cn'],
                    'product_name_en' => $val['product_name_en'],
                    'sku_id' => $sku_id,
                    'sku_name_cn' => $val['sku_name_cn'],
                    'sku_name_en' => $val['sku_name_en'],
                    'unit_price' => $val['unit_price'],
                    'count' => $val['count'],
                    'price' => $val['price'],
                    'pic_need' => $val['pic_need'],
                    'range_min' => $val['range_min'],
                    'range_max' => $val['range_max'],
                );
                $item_id = $CMysql->addRow('dv_order_item', $item_data);

                if($order_from == 1){
                    $product_id = $val['package_id'];
                    $package_pid = $val['product_id'];
                    $package_sku = $val['package_sku'];
                    $sql = "select pic_id,pic_name,pic_dir,folder_name from dv_package_pic where user_id = ".$this->user_id." and fetch_code = '$fetch_code' and product_id = $product_id and package_pid = $package_pid and package_sku = $package_sku and del = 0";
                    $picRows = $CMysql->getRowsBySql($sql);
                }else{
                    $pic_id = $val['pic_id'];
                    //查询图片信息
                    $sql = "select t1.id as pic_id,t1.name as pic_name,t1.dir as pic_dir,t2.name as folder_name from dv_pic t1"
                        ." left join dv_pic_folder t2 on t1.fid = t2.id and t2.level = 3"
                        ." where t1.id = $pic_id";
                    $picRows = $CMysql->getRowsBySql($sql);
                }

                if(!empty($picRows)){
                    foreach ($picRows as $k => $v) {
                        $Attr = [];
                        //插入选中的照片
                        $Attr = array(
                            'item_id' => $item_id,
                            'order_id' => $order_id,
                            'user_id' => $this->customer_user_id,
                            'fetch_code' => $fetch_code,
                            'school_id' => $school_id,
                            'pic_id' => $v['pic_id'],
                            'pic_name' => $v['pic_name'],
                            'pic_dir' => $v['pic_dir'],
                            'folder_name' => $v['folder_name'],
                            'status' => 2,
                            'created_at' => $this->system_time,
                            'updated_at' => $this->system_time,
                        );
                        $order_pic_id = $CMysql->addRow('dv_order_pic',$Attr);
                        if(!$order_pic_id){
                            $this->render('400', 'error', "Failed to select photos");
                        }
                        //更新订单产品已选照片数量
                        $sql = "update dv_order_item set pic_selected = pic_selected+1 where item_id = $item_id";
                        $CMysql->execute($sql);
                    }
                }

                //日志数据
                $log_data[$sku_id] = array(
                    'product_id' => $val['product_id'],
                    'sku_id' => $sku_id,
                    'count' => $val['count'],
                );
            }
            
            if($order_from == 1){
                //清除套餐选片记录
                $upt_sql = "update dv_package_pic set del = 1, update_time = ".time()." where user_id = ".$this->user_id." and fetch_code = '$fetch_code' and product_id = $items and del = 0";
                $CMysql->execute($upt_sql);
            }else{
                //3：下单完结后，更新购物车内产品数据
                foreach ($items as $key => $data_id) {
                    unset($cart_items[$data_id]);
                }
                $this->updateCartData($CMysql,$cart_items,$cart_id);
            }

            //4.订单日志
            $log_desc = "创建了订单，订单详情：" . json_encode($log_data);
            $order_info = $CMysql->getRow('dv_order_item', array('order_id' => $order_id));
            $log_item_after = json_encode($order_info);
            $logAttr = array(
                'order_id' => $order_id,
                'desc' => $log_desc,
                'created_at' => $this->system_time,
                'info_before' => '',
                'info_after' => $log_item_after,
                'is_show' => 1,
                'operator_id' => isset($this->user_id) ? $this->user_id : '0',
                'operator_name' => isset($this->login_name) ? $this->login_name : 'System',
            );
            $CMysql->addRow('dv_order_log', $logAttr);

            $CMysql->commit();
        } catch (Exception $e) {
            $CMysql->rollback();
            return array("code"=>'400',"info"=>'创建订单失败'.$e->getMessage());
        }
        
        return array("code"=>'200',"info"=>$order_id);
    }

    /**
     * 获取用户地址详情
     * @param  [type] $addr_id [description]
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    private function getUserAddrById($CMysql,$addr_id,$user_id)
    {
        $addr_sql = "select t.*,xap.area_name as province_name,xac.area_name as city_name,xaa.area_name as area_name from dv_user_address t
                left join dv_area xap on xap.id = t.province_id
                left join dv_area xac on xac.id = t.city_id
                left join dv_area xaa on xaa.id = t.area_id
                 where t.address_id = $addr_id and t.user_id = $user_id";
        $address_data = $CMysql->getRowBySql($addr_sql);

        return $address_data;
    }

    /**
     * 更新购物车
     * @param  [type] $CMysql [description]
     * @return [type]         [description]
     */
    private function updateCartData($CMysql,$cart_items = [],$cart_id)
    {
        $price = 0.0;
        $sum = 0;
        //商品种类数量
        $count = count($cart_items);
        if(!empty($cart_items)){
            foreach ($cart_items as $k => $item) {
                //商品价格
                $shop_price = Common::getProductPrice($k,$item);
                //价格
                $price += $item['count'] * $shop_price;
                //总数
                $sum += $item['count'];
            }
        }
        $cart_data = array(
            'items' => json_encode($cart_items),
            'total_price' => $price,
            'total_count' => $sum,
            'item_count' => $count,
            'update_time' => $this->system_time,
        );
        //更新购物车
        $map['cart_id'] = $cart_id;
        $CMysql->updateRow('dv_es_cart',$cart_data,$map);
    }

    /**
     * 计算优惠券
     * @param  [type]  $user_id   [description]
     * @param  [type]  $coupon_id [description]
     * @param  [type]  $tag       [description]
     * @param  integer $sku_id    [description]
     * @return [type]             [description]
     */
    private function _orderCouponPrice($CMysql,$user_id,$coupon_id,$goods_amount,$order_id)
    {
        $time = time();
        //优惠金额
        $coupon_amount = 0.00;
        //订单金额
        $order_amount = $goods_amount;
        $price = $goods_amount;

        $pk_id = $coupon_id;
        $coupon_sql = "select * from dv_user_coupon where user_id = $user_id and pk_id = $pk_id and status = 0 and case when is_long = 1 then 1=1 when is_long = 2 then $time >= start_time and $time <= end_time end";
        $coupons = $CMysql->getRowBySql($coupon_sql);
        if(!empty($coupons)){
            if($coupons['range'] == 2){   //指定商品
                $total = $goods_amount;
                if($coupons['type'] == 1){  //折扣券
                    $coupon_amount = $total-($total*$coupons['discount']);//优惠金额
                    $order_amount = $price - $coupon_amount; //实际订单金额
                }else if($coupons['type'] == 2){ //满减券
                    if($total >= $coupons['total']){
                        $coupon_amount = $coupons['reduce']; //优惠金额
                        $order_amount = $price - $coupon_amount;//实际订单金额
                    }
                }else if($coupons['type'] == 3){  //无门槛券
                    $coupon_amount = $coupons['reduce']; //优惠金额
                    $order_amount = $price - $coupon_amount;//实际订单金额
                }
            }else{ //全品类
                if($coupons['type'] == 1){  //折扣券
                    $coupon_amount = $price-($price*$coupons['discount']);//优惠金额
                    $order_amount = $price*$coupons['discount']; //实际订单金额
                }else if($coupons['type'] == 2){  //满减券
                    if($price >= $coupons['total']){
                        $coupon_amount = $coupons['reduce']; //优惠金额
                        $order_amount = $price - $coupon_amount; //实际订单金额
                    }
                }else if($coupons['type'] == 3){ //无门槛券
                    $coupon_amount = $coupons['reduce']; //优惠金额
                    $order_amount = $price - $coupon_amount;//实际订单金额
                }
            }
            //优惠券更新状态
            if($coupon_amount > 0){
                //优惠券更新状态
                $cu = $CMysql->updateRow('dv_user_coupon',array('order_id'=>$order_id,'status' => 1,'updated_at' => $time),array('pk_id' => $pk_id));
                //活动优惠券更新
                $activity_coupon_update_sql = "update dv_activity_coupon set used=used+1 where activity_id = ".$coupons['activity_id']." and coupon_id = ".$coupons['coupon_id'];
                $acus = $CMysql->execute($activity_coupon_update_sql);
                //优惠券使用日志
                $oc_log_desc = "订单使用了优惠券，优惠金额：".$coupon_amount;
                $order_coupons = array(
                    'order_id' => $order_id,
                    'desc' => $oc_log_desc,
                    'created_at' => $time,
                    'info_before' => '',
                    'info_after' => json_encode($coupons),
                    'is_show' => 1,
                    'operator_id' => isset($user_id) ? $user_id : '0',
                    'operator_name' => isset($this->login_name) ? $this->login_name : 'System',
                );
                $CMysql->addRow('dv_order_log', $order_coupons);
            }
        }
        //计算完成后的金额和优惠
        $res['order_amount'] = $order_amount;
        $res['discount'] = $coupon_amount;
        return $res;
    }
}
