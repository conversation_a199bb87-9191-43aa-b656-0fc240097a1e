<?php

/**
 * 通用方法 
 *
 * <AUTHOR>
 */
class Common
{

    // 得到图片信息
    // s:小图 m:中图 b:大图
    public static function get_img_url($group, $path, $img_name, $prefix)
    {
        if ($img_name) {
            $config = x5()->import('config/config.ini.php');
            $host = $config['img_host'];
            if ($path) {
                if ($prefix) {
                    return $host . $group . '/' . $path . '/' . $prefix . '_' . $img_name;
                } else {
                    return $host . $group . '/' . $path . '/' . $img_name;
                }
            } else {
                if ($prefix) {
                    return $host . $group . '/' . $prefix . '_' . $img_name;
                } else {
                    return $host . $group . '/' . $img_name;
                }
            }
        } else {
            return '';
        }
    }

    /**
     * 阿里云短信
     * @param  [type] $mobile   [手机号]
     * @param  [type] $content  [短信内容]
     * @param  [type] $template [模板ID]
     * @return [type]           [返回值]
     */
    public static function aliyunSms($mobile, $content = '', $template)
    {
        x5()->import('/lib/aliyun/sms/AliyunSendSms.php');
        //短信签名
        $sign = '福址华龄';
        $result = AliyunSendSms::sendSms($mobile, $content, $sign, $template);
        if ($result['Code'] == 'OK') {
            return array("status" => '100');
        } else {
            return array("status" => '500');
        }
    }

    /**
     * 短信发送限制
     */
    public static function checkSms($mobile)
    {
        $time = time();
        //发送限制
        //1.同一个手机号1分钟1次1小时5次1天20次
        $start_d = strtotime(date('Y-m-d', $time));
        $end_d = $start_d + 86400;
        $time_h = $time - 3600;
        $time_m = $time - 60;
        $resource = new Resource();
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);
        //1分钟
        $count_m_sql = "select count(*) as count from fzh_mobile_log where mobile = '$mobile' and add_time >= $time_m and add_time <= $time";
        $count_m = $CMysql->getRowBySql($count_m_sql);
        if ($count_m['count'] >= 1) {
            return array('code' => 400, 'msg' => '操作过于频繁，请稍后重试!');
        }
        //1小时
        $count_h_sql = "select count(*) as count from fzh_mobile_log where mobile = '$mobile' and add_time >= $time_h and add_time <= $time";
        $count_h = $CMysql->getRowBySql($count_h_sql);
        if ($count_h['count'] >= 5) {
            return array('code' => 400, 'msg' => '操作过于频繁，请稍后重试!');
        }
        //1天
        $count_d_sql = "select count(*) as count from fzh_mobile_log where mobile = '$mobile' and add_time >= $start_d and add_time < $end_d";
        $count_d = $CMysql->getRowBySql($count_d_sql);
        if ($count_d['count'] >= 20) {
            return array('code' => 400, 'msg' => '操作过于频繁，请稍后重试!');
        }
        return array('code' => 200);
    }

    /**
     * 传入json数据进行HTTP POST请求
     *
     * @param string $url $data_string
     * @return string
     */
    public static function http_post($url, $data_string, $timeout = 60)
    {
        //curl验证成功
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); //// 跳过证书检查 
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data_string)
        ));

        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            print curl_error($ch);
        }
        curl_close($ch);
        return $result;
    }

    /**
     * 获取IP
     */
    public static function getIP()
    {
        # 前端服务器使用了nginx proxy ，必须才能获取外网的真实IP
        if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"]))
            $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        else if (!empty($_SERVER["REMOTE_ADDR"]))
            $ip = $_SERVER["REMOTE_ADDR"];
        else
            $ip = "Unknown";
        return $ip;
    }

    /**
     * 生成用户Token
     */
    public static function getToken($mobile)
    {
        include APP_PATH . '/config/apps.ini.php';
        $salt = $appsList['dave_mini']['key'];
        $str1 = md5(base64_encode($mobile . $salt));
        $rev = strrev($mobile);
        $str2 = md5(base64_encode($salt . $rev));
        return strtoupper(md5($str1) . $str2);
    }

    /**
     * 校验Token
     */
    public static function checkToken($token, $mobile)
    {
        return strtoupper($token . $mobile);
    }

    /**
     * 模拟HTTP请求
     */
    public static function HttpCurlMobile($url, $method = 'POST', $params = '')
    {
        $headers = array('application/x-www-from-urlencoded');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        }
        //如果为0，则直接将返回的数据输出，为1，则将值付给$file_content
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //设置cURL允许执行的最长秒数
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        //在发起连接前等待的时间，如果设置为0，则不等待
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        //http返回成功与否的状态
        curl_setopt($ch, CURLOPT_HEADER, 0);
        switch ($method) {
            case "GET":
                curl_setopt($ch, CURLOPT_HTTPGET, true);
                break;
            case "POST":
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
                break;
            case "DELETE":
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
                break;
        }
        $result = curl_exec($ch); //获得返回值
        $curl_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($curl_code === 204) {
            $result = $curl_code;
        }
        curl_close($ch);
        return $result;
    }

    /**
     * 生成唯一的单据ID：16位
     */
    public static function getUUID($prefix = '')
    {
        $time = time();
        $prefix = !empty($prefix) ? $prefix : 'SS';
        return $prefix . date("ymd") . sprintf('%05d', (date('s', $time) + 60 * date('i', $time) + 3600 * date('H', $time))) . substr(microtime(), 5, 2) . rand(10, 99);
    }

    /**
     * 生成唯一的退款单据ID：12位
     */
    public static function getRUID()
    {
        return date("ymdH") . substr(microtime(), 5, 2) . rand(10, 99);
    }

    public static function printLog($msg, $isOut = 0)
    {
        if ($isOut) {
            echo date("Y/m/d H:i:s") . ' ' . $msg . "\n\r";
        }
    }

    /**
     * 计算运行时间
     * @param int $lastTime  上次运行时间
     */
    public static function countRunTime(&$lastTime)
    {
        $runTime = microtime(true) - $lastTime;
        $lastTime = microtime(true);
        return $runTime;
    }

    /**
     * 从数组里取出属于为$value的所有元素
     */
    public static function getAllByArrt($array, $attr, $key)
    {
        $ret = array();
        foreach ($array as $id => $value) {
            if ($value[$attr] == $key)
                $ret[$id] = $value;
        }
        return $ret;
    }

    /**
     * 过滤xml中非法字符
     * @param string $string
     * @return type
     */
    public static function xml_escape_string($string)
    {
        $str = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x80-\xFF]/u', '', $string);
        $strResult = str_replace(array('&', '<', '>'), array('&amp;', '&lt;', '&gt;'), $str);
        return $strResult;
    }

    public static function sendPostRequest($reqUrl, $sendParams, $timeout = 5)
    {
        $ch = curl_init();
        $options = array(
            CURLOPT_URL => $reqUrl,
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => $sendParams,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_USERAGENT => 'XM_OWMS',
        );
        curl_setopt_array($ch, $options);
        //execute the request      
        $result = curl_exec($ch);
        if ($errno = curl_errno($ch))
            throw new Exception("Send request error:" . curl_error($ch), 1009);
        return $result;
    }

    /**
     * 判断是否是JSON格式的字符串
     */
    public static function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     * 中文不转化js_encode
     */
    public static function json_encode_zh($code)
    {
        $code = json_encode(self::urlencodeAry($code));
        return urldecode($code);
    }

    /**
     * 中文json_encode-转化数据为urlencode
     */
    private static function urlencodeAry($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                $data[$key] = self::urlencodeAry($val);
            }
            return $data;
        } else {
            return urlencode($data);
        }
    }

    public static function writeSolarLog($project, $data)
    {
        $project = str_replace('.', '', $project);
        @error_log(json_encode($data) . "\n", 3, '/home/<USER>/logs/wms.api.xiaomi.com/' . $project . '/' . date("Ymd") . '.log');
    }

    /**
     * 图片上传
     * $image：图片base64加密
     */
    public static function uploadImage($image, $group, $catalog = true)
    {
        $imageName = "cb" . date("YmdHis", time()) . rand(11, 99);
        $imageName = $imageName . '.png';
        if (strstr($image, ",")) {
            $image = explode(',', $image);
            $image = $image[1];
        }
        if ($catalog) {
            $path = "upload/" . $group . "/" . date("Ymd", time());
        } else {
            $path = "upload/" . $group;
        }
        if (!is_dir($path)) { //判断目录是否存在 不存在就创建
            mkdir($path, 0777, true);
        }
        $imageSrc =  $path . "/" . $imageName;  //图片名字
        $r = file_put_contents($imageSrc, base64_decode($image)); //返回的是字节数
        if ($r) {
            $imgSrc = array(
                'img_url' => $imageName,
            );
            if ($catalog) {
                $imgSrc['img_dir'] = date("Ymd", time());
            }
            return json_encode($imgSrc);
        } else {
            return false;
        }
    }
    
    /**
     * 加密
     *
     * @param [type] $param
     * @param [type] $skey
     * @return string 加密串
     */
    public static function encode($param, $skey)
    {
        $mode = 'aes-128-ecb';
        $skey = strtolower($skey);
        $content = $param;
        $result = openssl_encrypt($content, $mode, hex2bin($skey), true);
        return strtoupper(bin2hex($result));
    }

    /**
     * 解密
     *
     * @param [type] $param
     * @param [type] $skey
     * @return string 解密串
     */
    public static function decode($param, $skey)
    {
        $mode = 'aes-128-ecb';
        $skey = strtolower($skey);
        $content = hex2bin($param);
        $result = openssl_decrypt($content, $mode, hex2bin($skey), true);
        return $result;
    }

    /**
     * 获取格式化后的时间
     * @param  [type] $time [description]
     * @param  string $rule [description]
     * @return [type]       [description]
     */
    public static function getFormatTime($time = 0, $rule = 'Y-m-d H:i:s')
    {
        $FormatTime = '-';
        if ($time > 0) {
            $FormatTime = date($rule, $time);
        }
        return $FormatTime;
    }

    /**
     * 获取完整的HOST
     * @return [type] [description]
     */
    public static function getHost()
    {
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $back_url = $http_type . $_SERVER['HTTP_HOST'];
        return $back_url;
    }

    //emoji转字符串
    public static function emojiTostr($str)
    {
        $strEncode = '';
        $length = mb_strlen($str, 'utf-8');
        for ($i = 0; $i < $length; $i++) {
            $_tmpStr = mb_substr($str, $i, 1, 'utf-8');
            if (strlen($_tmpStr) >= 4) {
                $strEncode .= '[[EMOJI:' . rawurlencode($_tmpStr);
            } else {
                $strEncode .= $_tmpStr;
            }
        }
        return $strEncode;
    }

    public static function strToemoji($str)
    {
        $str_arr = explode("[[EMOJI:", $str);
        $new_str = "";
        foreach ($str_arr as $key => $val) {
            $new_str .= rawurldecode($val);
        }
        return $new_str;
    }

    // 过滤掉emoji表情
    public static function emojiReject($text)
    {
        $len = mb_strlen($text);
        $new_text = '';
        for ($i = 0; $i < $len; $i++) {
            $word = mb_substr($text, $i, 1);
            if (strlen($word) <= 3) {
                $new_text .= $word;
            }
        }
        return $new_text;
    }

    /**
     * 生成随机session
     * @param  [type] $len [description]
     * @return [type]      [description]
     */
    public static function _3rd_session($len)
    {
        $fp = @fopen('/dev/urandom', 'rb');

        $result = '';

        if ($fp !== FALSE) {
            $result .= @fread($fp, $len);
            @fclose($fp);
        } else {
            trigger_error('Can not open /dev/urandom.');
        }

        // convert from binary to string
        $result = base64_encode($result);

        // remove none url chars
        $result = strtr($result, '+/', '-_');

        return substr($result, 0, $len);
    }

    /**
     * 获取城市名称
     */
    public static function getAreaName($area_id)
    {
        $resource = new Resource();
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);
        $areaName = $CMysql->getRow('fzh_area', array('id' => $area_id));
        return $areaName['area_name'];
    }

    /**
     * order-log
     */
    public static function orderLog($order_id, $desc, $info_before, $info_after, $user_id, $user_name)
    {
        //加载数据库操作类
        $resource = new Resource();
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);
        //日志数据
        $logAttr = array(
            'order_id' => $order_id,
            'desc' => $desc,
            'info_before' => $info_before,
            'info_after' => $info_after,
            'is_show' => 1,
            'operator_id' => $user_id,
            'operator_name' => $user_name,
            'created_at' => time()
        );
        $n = $CMysql->addRow('fzh_order_log', $logAttr);
        if (!$n) {
            throw new Exception('Order操作日志记录失败！' . json_encode($logAttr), 6001);
        }
    }

    /**
     * mobile-log
     * 短信发送日志表
     */
    public static function mobileLog($mobile, $msg)
    {
        $time = time();
        $resource = new Resource();
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);
        $logAttr = array(
            'mobile' => $mobile,
            'msg' => $msg,
            'add_time' => $time,
            'ip' => self::getIP(),
        );
        $n = $CMysql->addRow('fzh_mobile_log', $logAttr);
        if (!$n) {
            throw new Exception('短信发送日志记录失败！' . json_encode($logAttr), 6001);
        }
    }

    /**
     * 二维数组根据某个字段排序
     * @param array $array 要排序的数组
     * @param string $keys   要排序的键字段
     * @param string $sort  排序类型  SORT_ASC     SORT_DESC 
     * @return array 排序后的数组
     */
    public static function arraySort($array, $keys, $sort = SORT_DESC)
    {
        $keysValue = [];
        foreach ($array as $k => $v) {
            $keysValue[$k] = $v[$keys];
        }
        array_multisort($keysValue, $sort, $array);
        return $array;
    }
}
