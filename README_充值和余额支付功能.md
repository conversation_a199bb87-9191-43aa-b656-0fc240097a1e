# 充值和余额支付功能实现

## 功能概述

为小程序项目实现了完整的充值和余额支付功能，包括：

1. **充值功能**：用户可以通过微信支付为账户充值
2. **余额支付功能**：用户可以使用账户余额支付订单
3. **统一支付接口**：支持选择微信支付或余额支付
4. **余额管理**：查看余额、余额变动记录等

## 已实现的文件

### 1. 数据库结构
- `database_structure.sql` - 数据库表结构SQL文件

### 2. 控制器文件
- `www/controllers/mini/v1/Recharge.php` - 充值接口
- `www/controllers/mini/v1/RechargeNotify.php` - 充值微信支付回调
- `www/controllers/mini/v1/BalancePay.php` - 余额支付接口
- `www/controllers/mini/v1/BalanceLog.php` - 余额记录查询接口
- `www/controllers/mini/v1/Payment.php` - 修改后的统一支付接口
- `www/controllers/mini/v1/User.php` - 修改后的用户信息接口（包含余额）

### 3. 配置文件
- `www/config/config.example.php` - 添加了充值回调配置

### 4. 文档和测试
- `API_Documentation.md` - 详细的API接口文档
- `test_api.php` - 接口测试脚本
- `README_充值和余额支付功能.md` - 本文档

## 数据库表结构

### 1. 用户表修改
```sql
ALTER TABLE `fzh_user` ADD COLUMN `balance` DECIMAL(10,2) DEFAULT 0.00 COMMENT '用户余额';
```

### 2. 新增表
- `fzh_recharge_record` - 充值记录表
- `fzh_balance_pay_record` - 余额支付记录表
- `fzh_balance_log` - 用户余额变动记录表

## 接口说明

### 充值流程
1. 用户调用 `/mini/v1/Recharge` 接口发起充值
2. 系统创建充值记录，返回微信支付参数
3. 用户完成微信支付
4. 微信回调 `/mini/v1/RechargeNotify` 接口
5. 系统更新用户余额和充值记录状态

### 余额支付流程
1. 用户调用 `/mini/v1/Payment` 接口，设置 `pay_type=2`
2. 系统验证用户余额是否足够
3. 扣除用户余额，更新订单状态
4. 记录支付记录和余额变动日志

### 统一支付接口
- `pay_type=1`：微信支付（默认）
- `pay_type=2`：余额支付

## 安全特性

1. **事务处理**：所有涉及金额的操作都使用数据库事务
2. **余额验证**：支付前验证用户余额是否足够
3. **订单验证**：验证订单状态和用户权限
4. **金额精度**：使用DECIMAL类型确保金额精度
5. **操作日志**：记录所有余额变动和支付操作

## 部署步骤

1. **执行数据库脚本**
   ```bash
   mysql -u username -p database_name < database_structure.sql
   ```

2. **更新配置文件**
   - 在 `config.ini.php` 中添加充值回调地址配置
   ```php
   'recharge_notify' => 'https://your-domain.com/mini/v1/RechargeNotify'
   ```

3. **测试接口**
   - 修改 `test_api.php` 中的配置
   - 运行测试脚本验证功能

## 注意事项

1. **充值金额限制**：1-10000元
2. **余额精度**：保留两位小数
3. **回调配置**：需要在微信支付配置中设置正确的回调地址
4. **权限验证**：所有接口都需要用户登录token
5. **错误处理**：完善的错误提示和异常处理

## 扩展功能建议

1. **充值优惠**：可以添加充值赠送功能
2. **余额提现**：实现余额提现到微信零钱
3. **余额转账**：用户之间余额转账功能
4. **充值套餐**：预设充值金额套餐
5. **余额冻结**：支持余额冻结和解冻功能

## 技术特点

- 遵循项目现有的代码规范和架构
- 使用项目现有的数据库操作类和基础框架
- 完善的错误处理和日志记录
- 支持分页查询和数据筛选
- 事务处理确保数据一致性

## 联系方式

如有问题或需要进一步的功能扩展，请联系开发团队。
