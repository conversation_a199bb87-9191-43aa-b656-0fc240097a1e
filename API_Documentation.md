# 充值和余额支付接口文档

## 数据库结构

在使用接口前，需要先执行 `database_structure.sql` 中的SQL语句来创建相关的数据库表结构。

## 接口列表

### 1. 充值接口（已优化）

**接口地址：** `/mini/v1/Recharge`

**请求方式：** POST

**请求参数：**
```json
{
    "token": "用户登录token",
    "amount": 100.00
}
```

**参数说明：**
- `token`: 用户登录token（必填）
- `amount`: 充值金额，范围1-10000元（必填）

**返回示例：**
```json
{
    "header": {
        "code": 200,
        "desc": "success"
    },
    "body": {
        "order_id": "CZ2312250001",
        "order_type": 3,
        "amount": "100.00",
        "payment_params": {
            "appId": "wx9dbf41e2035c1de5",
            "package": "prepay_id=wx123456789",
            "signType": "MD5",
            "nonceStr": "abc123",
            "timeStamp": "1640995200",
            "paySign": "签名字符串"
        }
    }
}
```

**说明：** 充值接口现在会创建一个充值订单（order_type=3），然后返回订单信息和微信支付参数。

### 2. ~~余额支付接口~~（已删除）

**说明：** `BalancePay` 接口已被删除，余额支付功能已整合到统一支付接口 `Payment` 中。

### 3. 统一支付接口（已优化）

**接口地址：** `/mini/v1/Payment`

**请求方式：** POST

**请求参数：**
```json
{
    "token": "用户登录token",
    "order_id": "DD2312250001",
    "pay_type": 1
}
```

**参数说明：**
- `token`: 用户登录token（必填）
- `order_id`: 订单号，支持商品订单和充值订单（必填）
- `pay_type`: 支付方式，1-微信支付，2-余额支付（可选，默认为1）

**微信支付返回示例：**
```json
{
    "header": {
        "code": 200,
        "desc": "success"
    },
    "body": {
        "appId": "wx9dbf41e2035c1de5",
        "package": "prepay_id=wx123456789",
        "signType": "MD5",
        "nonceStr": "abc123",
        "timeStamp": "1640995200",
        "paySign": "签名字符串"
    }
}
```

**余额支付返回示例：**
```json
{
    "header": {
        "code": 200,
        "desc": "支付成功"
    },
    "body": {
        "pay_id": "YE2312250001",
        "order_id": "DD2312250001",
        "pay_amount": "99.00",
        "balance_after": "1.00",
        "pay_time": "2023-12-25 10:30:00",
        "pay_type": "balance"
    }
}
```

### 4. 用户信息接口（已修改）

**接口地址：** `/mini/v1/User`

**请求方式：** POST

**请求参数：**
```json
{
    "token": "用户登录token"
}
```

**返回示例：**
```json
{
    "header": {
        "code": 200,
        "desc": "success"
    },
    "body": {
        "user_id": 123,
        "user_name": "13800138000",
        "tel": "13800138000",
        "balance": "100.00"
    }
}
```

### 5. 余额记录查询接口

**接口地址：** `/mini/v1/BalanceLog`

**请求方式：** POST

**请求参数：**
```json
{
    "token": "用户登录token",
    "page": 1,
    "pageSize": 20,
    "change_type": 0
}
```

**参数说明：**
- `token`: 用户登录token（必填）
- `page`: 页码，默认1（可选）
- `pageSize`: 每页数量，默认20，最大100（可选）
- `change_type`: 变动类型筛选，0-全部，1-充值，2-支付，3-退款（可选）

**返回示例：**
```json
{
    "header": {
        "code": 200,
        "desc": "success"
    },
    "body": {
        "list": [
            {
                "change_type": 1,
                "change_type_text": "充值",
                "change_amount": "100.00",
                "balance_before": "0.00",
                "balance_after": "100.00",
                "related_id": "CZ2312250001",
                "create_time": "2023-12-25 10:00:00",
                "remark": "充值成功，充值金额：100.00元"
            }
        ],
        "page": 1,
        "pageSize": 20,
        "total": 1,
        "totalPages": 1
    }
}
```

## 回调接口

### 充值回调接口

**接口地址：** `/mini/v1/RechargeNotify`

**说明：** 微信支付充值成功后的回调接口，用于更新用户余额。需要在微信支付配置中设置此回调地址。

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误或业务逻辑错误
- `401`: 未登录或token过期

## 代码优化

### 微信支付公共类

为了消除代码重复，我们创建了 `www/lib/WechatPay.php` 微信支付公共类，统一处理：

- 微信支付统一下单
- 签名计算和验证
- 回调数据处理
- XML和数组转换

### 重构的文件

1. **Recharge.php** - 使用WechatPay类，删除重复的微信支付代码
2. **Payment.php** - 使用WechatPay类，删除重复的微信支付代码
3. **RechargeNotify.php** - 使用WechatPay类统一处理回调
4. **WXNotify.php** - 使用WechatPay类统一处理回调

### 优化效果

- 减少了约200行重复代码
- 统一了微信支付处理逻辑
- 提高了代码可维护性
- 增强了错误处理和安全性

## 注意事项

1. 所有接口都需要用户登录token
2. 充值金额限制在1-10000元之间
3. 余额支付前会验证用户余额是否足够
4. 所有金额都保留两位小数
5. 需要在配置文件中添加充值回调地址配置
6. 数据库操作使用事务确保数据一致性
7. 微信支付回调已统一使用WechatPay类处理，提高了安全性
